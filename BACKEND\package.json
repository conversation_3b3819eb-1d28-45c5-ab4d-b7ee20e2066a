{"name": "backend", "version": "1.0.0", "description": "DevConnect server", "license": "ISC", "author": "<PERSON><PERSON><PERSON>", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:watch": "nodemon --watch . --ext js,json server.js", "create-admin": "node scripts/createAdmin.js", "reset-admin-password": "node scripts/resetAdminPassword.js", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf node_modules uploads/*.jpg uploads/*.png uploads/*.jpeg"}, "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^2.0.1", "multer-storage-cloudinary": "^4.0.0", "socket.io": "^4.8.1"}, "devDependencies": {"concurrently": "^9.1.2", "nodemo": "^1.0.0", "nodemon": "^3.1.10"}}