import userModel from "../models/userModel.js";

export const getUsers = async (req, res) => {
    try{
        const users = await userModel.find({}).select("-password");
        res.status(200).json({
            msg: "Users fetched successfully",
            users,
        });
    }
    catch(err){
        console.error("Get users error:", err);
        res.status(500).json({ msg: "Server error during get users" });
    }
}

export const deleteUser = async (req, res) => {
    try {
        const { userId } = req.params;

        const deletedUser = await userModel.findByIdAndDelete(userId);

        if (!deletedUser) {
            return res.status(404).json({ msg: "User not found" });
        }

        res.status(200).json({
            msg: "User deleted successfully",
            deletedUser: {
                id: deletedUser._id,
                username: deletedUser.username,
                email: deletedUser.email
            }
        });
    } catch (err) {
        console.error("Delete user error:", err);
        res.status(500).json({ msg: "Server error during user deletion" });
    }
}
